import { Suspense } from "react"
import { CalendarDays, Users, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { PTOBalanceCard, PTOBalanceSkeleton } from "@/components/pto-balance-card"
import { PTORequestForm } from "@/components/pto-request-form"
import { PTORequestsTable } from "@/components/pto-requests-table"
import { requirePermission, getCurrentUser } from "@/lib/auth"
import { getPTODashboardData } from "@/lib/data"

// Statistics cards component
function StatsCards({ stats, viewMode }: { stats: any, viewMode: 'manager' | 'employee' }) {
  const statCards = [
    {
      title: "Total Requests",
      value: stats.totalRequests,
      icon: CalendarDays,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Pending",
      value: stats.pendingRequests,
      icon: Clock,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
    {
      title: "Approved",
      value: stats.approvedRequests,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Rejected",
      value: stats.rejectedRequests,
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-4">
      {statCards.map((card) => {
        const Icon = card.icon
        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <div className={`${card.bgColor} p-2 rounded-full`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {viewMode === 'manager' ? 'From your team' : 'Your requests'}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// Main dashboard content
async function PTODashboardContent() {
  const dashboardData = await getPTODashboardData()

  if (!dashboardData) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to load PTO data</h3>
        <p className="text-gray-500 text-center max-w-md">
          There was an issue loading your PTO information. Please try refreshing the page or contact support if the problem persists.
        </p>
      </div>
    )
  }

  if (dashboardData.type === 'manager') {
    // Manager view
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">PTO Management</h1>
            <p className="text-muted-foreground">
              Manage time off requests from your team members.
            </p>
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <Users className="mr-1 h-3 w-3" />
            Manager View
          </Badge>
        </div>

        <StatsCards stats={dashboardData.stats} viewMode="manager" />

        <div className="grid gap-6 lg:grid-cols-2">
          <PTORequestsTable
            requests={dashboardData.pendingRequests}
            viewMode="manager"
            title="Pending Approvals"
            description="PTO requests awaiting your approval"
            emptyMessage="No pending requests from your team."
          />
          
          <PTORequestsTable
            requests={dashboardData.teamRequests.filter((r: any) => r.status !== 'pending')}
            viewMode="manager"
            title="Recent Requests"
            description="Recently processed PTO requests"
            emptyMessage="No recent requests from your team."
          />
        </div>
      </div>
    )
  } else {
    // Employee view
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">My PTO</h1>
            <p className="text-muted-foreground">
              View your PTO balance and manage your time off requests.
            </p>
          </div>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <CalendarDays className="mr-1 h-3 w-3" />
            Employee View
          </Badge>
        </div>

        <StatsCards stats={dashboardData.stats} viewMode="employee" />

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-1">
            <PTOBalanceCard 
              balance={dashboardData.balance}
              className="mb-6"
            />
            
            <PTORequestForm
              balance={dashboardData.balance}
              trigger={
                <div className="w-full">
                  <button className="w-full flex items-center justify-center gap-2 bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    <CalendarDays className="h-4 w-4" />
                    Request Time Off
                  </button>
                </div>
              }
            />
          </div>
          
          <div className="lg:col-span-2">
            <PTORequestsTable
              requests={dashboardData.recentRequests}
              viewMode="employee"
              title="My PTO Requests"
              description="Your time off request history"
              emptyMessage="You haven't submitted any PTO requests yet."
            />
          </div>
        </div>
      </div>
    )
  }
}

// Loading skeleton
function PTODashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-64 bg-gray-200 rounded animate-pulse mt-2" />
        </div>
        <div className="h-6 w-24 bg-gray-200 rounded animate-pulse" />
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
              <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-12 bg-gray-200 rounded animate-pulse" />
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-1">
          <PTOBalanceSkeleton className="mb-6" />
          <div className="h-10 w-full bg-gray-200 rounded animate-pulse" />
        </div>
        
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-12 w-full bg-gray-200 rounded animate-pulse" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default async function PTOPage() {
  // Require PTO read permission to access this page
  await requirePermission('pto:read')

  // Check if user has access to PTO system
  const user = await getCurrentUser()
  const isSuperAdmin = user?.role === 'super-admin'
  const isMarketingUser = user?.email?.includes('natalie') || 
                          user?.email?.includes('marketing') ||
                          user?.fullName?.includes('Natalie')
  
  // Super-admins have unrestricted access to all systems
  if (!isSuperAdmin && !isMarketingUser) {
    return (
      <div className="p-4 sm:p-6">
        <div className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
          <p className="text-gray-500 text-center max-w-md">
            PTO system is currently available only to Marketing department members.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6">
      <Suspense fallback={<PTODashboardSkeleton />}>
        <PTODashboardContent />
      </Suspense>
    </div>
  )
}