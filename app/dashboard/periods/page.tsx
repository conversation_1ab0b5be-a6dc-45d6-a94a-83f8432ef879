import { getPeriods } from "@/lib/data"
import { getCurrentUser } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PeriodsTable } from "@/components/periods-table"
import { RoleGuard } from "@/components/role-guard"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield } from "lucide-react"

export default async function PeriodsPage() {
  const user = await getCurrentUser()

  if (!user) {
    return (
      <div className="p-4 sm:p-6">
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Authentication required. Please sign in to access this page.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <RoleGuard
      allowedRoles={['hr-admin', 'super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <PeriodManagementContent />
    </RoleGuard>
  )
}

async function PeriodManagementContent() {
  const periods = await getPeriods()

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Appraisal Period Management</h1>
        <p className="text-muted-foreground">Open new periods and close old ones.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>All Appraisal Periods</CardTitle>
          <CardDescription>A list of all past and current appraisal periods.</CardDescription>
        </CardHeader>
        <CardContent>
          <PeriodsTable data={periods} />
        </CardContent>
      </Card>
    </div>
  )
}
