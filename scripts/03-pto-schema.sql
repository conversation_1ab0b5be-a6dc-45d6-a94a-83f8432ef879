-- PTO (Paid Time Off) System Schema
-- This script creates the necessary tables and enums for the PTO system
-- 
-- PREREQUISITES: Run 01-schema.sql first to create the base database structure
-- 
-- FEATURES:
-- - PTO balance tracking per employee per year
-- - PTO request submission and approval workflow
-- - Role-based access control integration
-- - Audit trail for all PTO actions

-- Create PTO-specific enums
CREATE TYPE appy_pto_status AS ENUM ('pending', 'approved', 'rejected', 'cancelled');
CREATE TYPE appy_pto_request_type AS ENUM ('vacation', 'sick', 'personal', 'emergency');

-- Employee PTO Balances Table
-- Tracks annual PTO balances for each employee
CREATE TABLE appy_employee_pto_balances (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id uuid NOT NULL REFERENCES appy_employees(id) ON DELETE CASCADE,
  year integer NOT NULL DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
  total_days integer NOT NULL DEFAULT 7,
  used_days integer NOT NULL DEFAULT 0,
  available_days integer GENERATED ALWAYS AS (total_days - used_days) STORED,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(employee_id, year)
);

-- PTO Requests Table
-- Stores all PTO requests with approval workflow
CREATE TABLE appy_pto_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id uuid NOT NULL REFERENCES appy_employees(id) ON DELETE CASCADE,
  manager_id text NOT NULL, -- Clerk user ID (not UUID format)
  request_type appy_pto_request_type NOT NULL DEFAULT 'vacation',
  start_date date NOT NULL,
  end_date date NOT NULL,
  days_requested integer NOT NULL,
  reason text,
  status appy_pto_status NOT NULL DEFAULT 'pending',
  approved_by text, -- Clerk user ID
  approved_at timestamptz,
  rejected_reason text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT valid_date_range CHECK (end_date >= start_date),
  CONSTRAINT positive_days CHECK (days_requested > 0),
  CONSTRAINT max_days_per_request CHECK (days_requested <= 7)
);

-- Indexes for performance optimization
CREATE INDEX idx_pto_requests_employee_id ON appy_pto_requests(employee_id);
CREATE INDEX idx_pto_requests_manager_id ON appy_pto_requests(manager_id);
CREATE INDEX idx_pto_requests_status ON appy_pto_requests(status);
CREATE INDEX idx_pto_requests_dates ON appy_pto_requests(start_date, end_date);
CREATE INDEX idx_pto_balances_employee_year ON appy_employee_pto_balances(employee_id, year);

-- Trigger to update PTO balances when requests are approved
CREATE OR REPLACE FUNCTION update_pto_balance_on_approval()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update balance if status changed from non-approved to approved
  IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
    UPDATE appy_employee_pto_balances 
    SET used_days = used_days + NEW.days_requested,
        updated_at = now()
    WHERE employee_id = NEW.employee_id 
      AND year = EXTRACT(YEAR FROM NEW.start_date);
  END IF;
  
  -- If request was approved and now rejected/cancelled, subtract days back
  IF OLD.status = 'approved' AND NEW.status IN ('rejected', 'cancelled') THEN
    UPDATE appy_employee_pto_balances 
    SET used_days = used_days - NEW.days_requested,
        updated_at = now()
    WHERE employee_id = NEW.employee_id 
      AND year = EXTRACT(YEAR FROM NEW.start_date);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to PTO requests table
CREATE TRIGGER trg_update_pto_balance
  AFTER UPDATE ON appy_pto_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_pto_balance_on_approval();

-- Function to check if employee has sufficient PTO balance
CREATE OR REPLACE FUNCTION check_pto_balance(
  p_employee_id uuid,
  p_days_requested integer,
  p_start_date date
) RETURNS boolean AS $$
DECLARE
  v_available_days integer;
  v_year integer;
BEGIN
  v_year := EXTRACT(YEAR FROM p_start_date);
  
  SELECT available_days INTO v_available_days
  FROM appy_employee_pto_balances
  WHERE employee_id = p_employee_id AND year = v_year;
  
  -- If no balance record exists, assume they have 7 days available
  IF v_available_days IS NULL THEN
    v_available_days := 7;
  END IF;
  
  RETURN v_available_days >= p_days_requested;
END;
$$ LANGUAGE plpgsql;

-- Function to get PTO statistics for a manager
CREATE OR REPLACE FUNCTION get_manager_pto_stats(p_manager_id text)
RETURNS TABLE(
  total_requests integer,
  pending_requests integer,
  approved_requests integer,
  rejected_requests integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::integer as total_requests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END)::integer as pending_requests,
    COUNT(CASE WHEN status = 'approved' THEN 1 END)::integer as approved_requests,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END)::integer as rejected_requests
  FROM appy_pto_requests
  WHERE manager_id = p_manager_id
    AND created_at >= date_trunc('year', CURRENT_DATE);
END;
$$ LANGUAGE plpgsql;

-- Initialize PTO balances for existing employees
-- This will be run separately after the schema is created
-- INSERT INTO appy_employee_pto_balances (employee_id, total_days, used_days)
-- SELECT id, 7, 0 
-- FROM appy_employees 
-- WHERE active = true
-- ON CONFLICT (employee_id, year) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE appy_employee_pto_balances IS 'Tracks annual PTO balances for each employee';
COMMENT ON TABLE appy_pto_requests IS 'Stores all PTO requests with approval workflow';
COMMENT ON FUNCTION check_pto_balance(uuid, integer, date) IS 'Validates if employee has sufficient PTO balance for request';
COMMENT ON FUNCTION get_manager_pto_stats(text) IS 'Returns PTO statistics for a manager';

COMMIT;