/**
 * Utility script to set super-admin role in Clerk user metadata
 * 
 * This script helps set the 'super-admin' role in Clerk's publicMetadata
 * for users who should have super-admin access.
 * 
 * Usage:
 * 1. Replace USER_ID with the actual Clerk user ID
 * 2. Run: bun run scripts/set-super-admin-role.ts
 */

import { clerkClient } from '@clerk/nextjs/server'

// Configuration
const SUPER_ADMIN_USERS = [
  {
    email: '<EMAIL>',
    userId: 'user_REPLACE_WITH_ACTUAL_CLERK_USER_ID', // Replace with actual Clerk user ID
    fullName: '<PERSON>'
  },
  // Add more super-admin users as needed
]

async function setSuperAdminRole() {
  console.log('🚀 Setting super-admin roles in Clerk metadata...')
  
  for (const user of SUPER_ADMIN_USERS) {
    try {
      // Get the user to verify they exist
      const clerkUser = await clerkClient.users.getUser(user.userId)
      
      if (!clerkUser) {
        console.error(`❌ User not found: ${user.email} (${user.userId})`)
        continue
      }
      
      // Update user metadata with super-admin role
      await clerkClient.users.updateUserMetadata(user.userId, {
        publicMetadata: {
          ...clerkUser.publicMetadata,
          role: 'super-admin'
        }
      })
      
      console.log(`✅ Successfully set super-admin role for: ${user.email}`)
      
      // Also verify the user exists in the database
      const { supabase } = await import('../lib/supabase')
      
      // Check if user exists in managers table
      const { data: manager } = await supabase
        .from('appy_managers')
        .select('*')
        .eq('user_id', user.userId)
        .single()
      
      if (!manager) {
        console.log(`🔄 Creating manager record for: ${user.email}`)
        
        // Create manager record
        await supabase
          .from('appy_managers')
          .insert({
            user_id: user.userId,
            full_name: user.fullName,
            email: user.email,
            active: true
          })
      }
      
      // Ensure role exists in user_roles table
      await supabase
        .from('appy_user_roles')
        .upsert({
          user_id: user.userId,
          role: 'super-admin'
        })
      
      console.log(`✅ Database records updated for: ${user.email}`)
      
    } catch (error) {
      console.error(`❌ Error setting role for ${user.email}:`, error)
    }
  }
  
  console.log('🎉 Super-admin role setup completed!')
}

// Instructions for getting user ID
console.log(`
📋 INSTRUCTIONS:

1. Go to your Clerk Dashboard: https://dashboard.clerk.com
2. Navigate to Users section
3. Find the user (<EMAIL>)
4. Copy their User ID (starts with 'user_')
5. Replace 'user_REPLACE_WITH_ACTUAL_CLERK_USER_ID' in this script
6. Run: bun run scripts/set-super-admin-role.ts

🔧 Alternative: Manual Setup
1. In Clerk Dashboard, go to Users
2. <NAME_EMAIL>
3. Go to Metadata tab
4. Add to Public Metadata:
   {
     "role": "super-admin"
   }
5. Save changes

🗄️ Database Setup:
Make sure your database has the user record:
INSERT INTO appy_managers (user_id, full_name, email) 
VALUES ('your_actual_clerk_user_id', 'Bob Wazneh', '<EMAIL>');

INSERT INTO appy_user_roles (user_id, role) 
VALUES ('your_actual_clerk_user_id', 'super-admin');
`)

// Run the script
if (require.main === module) {
  setSuperAdminRole().catch(console.error)
}

export { setSuperAdminRole }