import { useEffect, useRef, useCallback } from 'react'

/**
 * Hook for managing focus in modals and dialogs
 */
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLElement>(null)
  const previousFocusRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isActive) return

    console.log('[A11Y] Setting up focus trap')
    
    // Store the previously focused element
    previousFocusRef.current = document.activeElement as HTMLElement

    const container = containerRef.current
    if (!container) return

    // Get all focusable elements
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    // Focus the first element
    firstElement?.focus()

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus()
          e.preventDefault()
        }
      }
    }

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        console.log('[A11Y] Escape key pressed, should close modal')
        // This would typically trigger a close callback
      }
    }

    document.addEventListener('keydown', handleTabKey)
    document.addEventListener('keydown', handleEscapeKey)

    return () => {
      console.log('[A11Y] Cleaning up focus trap')
      document.removeEventListener('keydown', handleTabKey)
      document.removeEventListener('keydown', handleEscapeKey)
      
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus()
      }
    }
  }, [isActive])

  return containerRef
}

/**
 * Hook for managing focus announcements
 */
export function useFocusAnnouncement() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    console.log(`[A11Y] Focus announcement (${priority}):`, message)
    
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', priority)
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = message
    
    document.body.appendChild(announcement)
    
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement)
      }
    }, 1000)
  }, [])

  return { announce }
}

/**
 * Hook for keyboard navigation in lists/grids
 */
export function useKeyboardNavigation<T extends HTMLElement>(
  items: T[],
  options: {
    orientation?: 'horizontal' | 'vertical' | 'both'
    wrap?: boolean
    onActivate?: (index: number) => void
  } = {}
) {
  const { orientation = 'vertical', wrap = true, onActivate } = options
  const currentIndexRef = useRef(0)

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    const { key } = e
    let newIndex = currentIndexRef.current

    switch (key) {
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          e.preventDefault()
          newIndex = wrap 
            ? (currentIndexRef.current + 1) % items.length
            : Math.min(currentIndexRef.current + 1, items.length - 1)
        }
        break
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          e.preventDefault()
          newIndex = wrap
            ? currentIndexRef.current === 0 ? items.length - 1 : currentIndexRef.current - 1
            : Math.max(currentIndexRef.current - 1, 0)
        }
        break
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          e.preventDefault()
          newIndex = wrap 
            ? (currentIndexRef.current + 1) % items.length
            : Math.min(currentIndexRef.current + 1, items.length - 1)
        }
        break
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          e.preventDefault()
          newIndex = wrap
            ? currentIndexRef.current === 0 ? items.length - 1 : currentIndexRef.current - 1
            : Math.max(currentIndexRef.current - 1, 0)
        }
        break
      case 'Home':
        e.preventDefault()
        newIndex = 0
        break
      case 'End':
        e.preventDefault()
        newIndex = items.length - 1
        break
      case 'Enter':
      case ' ':
        e.preventDefault()
        onActivate?.(currentIndexRef.current)
        return
    }

    if (newIndex !== currentIndexRef.current) {
      currentIndexRef.current = newIndex
      items[newIndex]?.focus()
      console.log('[A11Y] Keyboard navigation moved to index:', newIndex)
    }
  }, [items, orientation, wrap, onActivate])

  const setCurrentIndex = useCallback((index: number) => {
    if (index >= 0 && index < items.length) {
      currentIndexRef.current = index
      items[index]?.focus()
    }
  }, [items])

  return {
    handleKeyDown,
    setCurrentIndex,
    currentIndex: currentIndexRef.current
  }
}

/**
 * Hook for managing roving tabindex
 */
export function useRovingTabIndex(isActive: boolean) {
  const elementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    if (isActive) {
      element.setAttribute('tabindex', '0')
      console.log('[A11Y] Set tabindex to 0 for active element')
    } else {
      element.setAttribute('tabindex', '-1')
      console.log('[A11Y] Set tabindex to -1 for inactive element')
    }
  }, [isActive])

  return elementRef
}
