import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import type { AppraisalStatus } from "@/lib/types"
import { getStatusAnnouncement } from "@/lib/accessibility"

const statusMap: Record<AppraisalStatus, { label: string; className: string; description: string }> = {
  submitted: {
    label: "Submitted",
    description: "Appraisal has been submitted for review",
    className:
      "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-400 border-green-200 dark:border-green-700",
  },
  draft: {
    label: "Draft",
    description: "Appraisal is saved as draft and not yet submitted",
    className:
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-400 border-yellow-200 dark:border-yellow-700",
  },
  "not-started": {
    label: "Not Started",
    description: "Appraisal has not been started yet",
    className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700",
  },
}

export function StatusBadge({ status }: { status: AppraisalStatus }) {
  const { label, className, description } = statusMap[status]
  return (
    <Badge
      variant="outline"
      className={cn("font-medium", className)}
      aria-label={getStatusAnnouncement(status)}
      title={description}
    >
      {label}
    </Badge>
  )
}
