/**
 * Accessibility utilities and helpers for the appraisal tool
 */

// Screen reader announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  console.log(`[A11Y] Screen reader announcement (${priority}):`, message)
  
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

// Focus management
export function trapFocus(element: HTMLElement) {
  console.log('[A11Y] Setting up focus trap for element:', element)
  
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )
  
  const firstElement = focusableElements[0] as HTMLElement
  const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
  
  function handleTabKey(e: KeyboardEvent) {
    if (e.key !== 'Tab') return
    
    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus()
        e.preventDefault()
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus()
        e.preventDefault()
      }
    }
  }
  
  element.addEventListener('keydown', handleTabKey)
  
  // Focus first element
  firstElement?.focus()
  
  return () => {
    element.removeEventListener('keydown', handleTabKey)
  }
}

// Keyboard navigation helpers
export function handleArrowNavigation(
  event: KeyboardEvent,
  items: HTMLElement[],
  currentIndex: number,
  onIndexChange: (index: number) => void
) {
  console.log('[A11Y] Handling arrow navigation, current index:', currentIndex)
  
  switch (event.key) {
    case 'ArrowDown':
    case 'ArrowRight':
      event.preventDefault()
      const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
      onIndexChange(nextIndex)
      items[nextIndex]?.focus()
      break
    case 'ArrowUp':
    case 'ArrowLeft':
      event.preventDefault()
      const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
      onIndexChange(prevIndex)
      items[prevIndex]?.focus()
      break
    case 'Home':
      event.preventDefault()
      onIndexChange(0)
      items[0]?.focus()
      break
    case 'End':
      event.preventDefault()
      const lastIndex = items.length - 1
      onIndexChange(lastIndex)
      items[lastIndex]?.focus()
      break
  }
}

// ARIA helpers
export function generateId(prefix: string = 'a11y'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

export function getStatusAnnouncement(status: string): string {
  console.log('[A11Y] Getting status announcement for:', status)
  
  const announcements: Record<string, string> = {
    'submitted': 'Submitted for review',
    'draft': 'Saved as draft',
    'missing': 'Not yet started',
    'active': 'Active employee',
    'inactive': 'Inactive employee',
    'below-expectations': 'Performance below expectations',
    'meets-expectations': 'Performance meets expectations',
    'exceeds-expectations': 'Performance exceeds expectations'
  }
  
  return announcements[status] || status
}

// Form validation announcements
export function announceFormError(fieldName: string, error: string) {
  console.log('[A11Y] Announcing form error for field:', fieldName, 'Error:', error)
  announceToScreenReader(`Error in ${fieldName}: ${error}`, 'assertive')
}

export function announceFormSuccess(message: string) {
  console.log('[A11Y] Announcing form success:', message)
  announceToScreenReader(message, 'polite')
}

// Table accessibility helpers
export function getTableAnnouncement(rowCount: number, columnCount: number): string {
  console.log('[A11Y] Getting table announcement for rows:', rowCount, 'columns:', columnCount)
  return `Table with ${rowCount} rows and ${columnCount} columns`
}

export function getSortAnnouncement(column: string, direction: 'asc' | 'desc' | null): string {
  console.log('[A11Y] Getting sort announcement for column:', column, 'direction:', direction)
  
  if (!direction) {
    return `${column} column, not sorted`
  }
  
  return `${column} column, sorted ${direction === 'asc' ? 'ascending' : 'descending'}`
}

// Skip link helpers
export function createSkipLink(targetId: string, text: string): HTMLAnchorElement {
  console.log('[A11Y] Creating skip link to:', targetId, 'with text:', text)
  
  const skipLink = document.createElement('a')
  skipLink.href = `#${targetId}`
  skipLink.textContent = text
  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-primary focus:text-primary-foreground focus:px-4 focus:py-2 focus:rounded'
  
  return skipLink
}

// Loading state announcements
export function announceLoadingState(isLoading: boolean, context: string) {
  console.log('[A11Y] Announcing loading state:', isLoading, 'for context:', context)
  
  if (isLoading) {
    announceToScreenReader(`Loading ${context}`, 'polite')
  } else {
    announceToScreenReader(`${context} loaded`, 'polite')
  }
}

// Error boundary announcements
export function announceError(error: string, context?: string) {
  console.log('[A11Y] Announcing error:', error, 'in context:', context)
  
  const message = context ? `Error in ${context}: ${error}` : `Error: ${error}`
  announceToScreenReader(message, 'assertive')
}
