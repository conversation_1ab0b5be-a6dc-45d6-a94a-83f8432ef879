/**
 * Comprehensive logging and monitoring system
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: Record<string, any>
  error?: Error
  userId?: string
  sessionId?: string
  component?: string
  action?: string
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableStorage: boolean
  maxStorageEntries: number
  enableRemoteLogging: boolean
  remoteEndpoint?: string
}

class Logger {
  private config: LoggerConfig
  private logs: LogEntry[] = []
  private sessionId: string
  private userId?: string

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableStorage: true,
      maxStorageEntries: 1000,
      enableRemoteLogging: false,
      ...config
    }
    
    this.sessionId = this.generateSessionId()
    this.loadStoredLogs()
    
    console.log('[LOG] Logger initialized with config:', this.config)
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getLevelPriority(level: LogLevel): number {
    const priorities = { debug: 0, info: 1, warn: 2, error: 3, fatal: 4 }
    return priorities[level]
  }

  private shouldLog(level: LogLevel): boolean {
    return this.getLevelPriority(level) >= this.getLevelPriority(this.config.level)
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toISOString()
    const level = entry.level.toUpperCase().padEnd(5)
    const context = entry.context ? ` ${JSON.stringify(entry.context)}` : ''
    const component = entry.component ? ` [${entry.component}]` : ''
    const action = entry.action ? ` (${entry.action})` : ''
    
    return `${timestamp} ${level}${component}${action} ${entry.message}${context}`
  }

  private logToConsole(entry: LogEntry) {
    if (!this.config.enableConsole) return

    const message = this.formatMessage(entry)
    
    switch (entry.level) {
      case 'debug':
        console.debug(message, entry.error)
        break
      case 'info':
        console.info(message, entry.error)
        break
      case 'warn':
        console.warn(message, entry.error)
        break
      case 'error':
      case 'fatal':
        console.error(message, entry.error)
        break
    }
  }

  private storeLog(entry: LogEntry) {
    if (!this.config.enableStorage) return

    this.logs.push(entry)
    
    // Maintain max storage limit
    if (this.logs.length > this.config.maxStorageEntries) {
      this.logs = this.logs.slice(-this.config.maxStorageEntries)
    }

    // Store in localStorage if available
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem('app_logs', JSON.stringify(this.logs.slice(-100))) // Store last 100 logs
      } catch (error) {
        console.warn('[LOG] Failed to store logs in localStorage:', error)
      }
    }
  }

  private loadStoredLogs() {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const stored = localStorage.getItem('app_logs')
        if (stored) {
          this.logs = JSON.parse(stored)
          console.log(`[LOG] Loaded ${this.logs.length} stored logs`)
        }
      } catch (error) {
        console.warn('[LOG] Failed to load stored logs:', error)
      }
    }
  }

  private async sendToRemote(entry: LogEntry) {
    if (!this.config.enableRemoteLogging || !this.config.remoteEndpoint) return

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      })
    } catch (error) {
      console.warn('[LOG] Failed to send log to remote endpoint:', error)
    }
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error,
    component?: string,
    action?: string
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
      userId: this.userId,
      sessionId: this.sessionId,
      component,
      action
    }
  }

  setUserId(userId: string) {
    this.userId = userId
    console.log(`[LOG] User ID set: ${userId}`)
  }

  setConfig(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config }
    console.log('[LOG] Config updated:', this.config)
  }

  debug(message: string, context?: Record<string, any>, component?: string, action?: string) {
    if (!this.shouldLog('debug')) return
    
    const entry = this.createLogEntry('debug', message, context, undefined, component, action)
    this.logToConsole(entry)
    this.storeLog(entry)
    this.sendToRemote(entry)
  }

  info(message: string, context?: Record<string, any>, component?: string, action?: string) {
    if (!this.shouldLog('info')) return
    
    const entry = this.createLogEntry('info', message, context, undefined, component, action)
    this.logToConsole(entry)
    this.storeLog(entry)
    this.sendToRemote(entry)
  }

  warn(message: string, context?: Record<string, any>, component?: string, action?: string) {
    if (!this.shouldLog('warn')) return
    
    const entry = this.createLogEntry('warn', message, context, undefined, component, action)
    this.logToConsole(entry)
    this.storeLog(entry)
    this.sendToRemote(entry)
  }

  error(message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) {
    if (!this.shouldLog('error')) return
    
    const entry = this.createLogEntry('error', message, context, error, component, action)
    this.logToConsole(entry)
    this.storeLog(entry)
    this.sendToRemote(entry)
  }

  fatal(message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) {
    if (!this.shouldLog('fatal')) return
    
    const entry = this.createLogEntry('fatal', message, context, error, component, action)
    this.logToConsole(entry)
    this.storeLog(entry)
    this.sendToRemote(entry)
  }

  // Specialized logging methods
  userAction(action: string, context?: Record<string, any>, component?: string) {
    this.info(`User action: ${action}`, context, component, action)
  }

  apiCall(endpoint: string, method: string, duration?: number, status?: number) {
    const context = { endpoint, method, duration, status }
    if (status && status >= 400) {
      this.error(`API call failed: ${method} ${endpoint}`, undefined, context, 'API')
    } else {
      this.info(`API call: ${method} ${endpoint}`, context, 'API')
    }
  }

  performance(metric: string, value: number, unit: string = 'ms') {
    this.debug(`Performance: ${metric} = ${value}${unit}`, { metric, value, unit }, 'Performance')
  }

  security(event: string, context?: Record<string, any>) {
    this.warn(`Security event: ${event}`, context, 'Security', event)
  }

  // Get logs for debugging
  getLogs(level?: LogLevel, component?: string, limit?: number): LogEntry[] {
    let filtered = this.logs

    if (level) {
      filtered = filtered.filter(log => log.level === level)
    }

    if (component) {
      filtered = filtered.filter(log => log.component === component)
    }

    if (limit) {
      filtered = filtered.slice(-limit)
    }

    return filtered
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  // Clear logs
  clearLogs() {
    this.logs = []
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('app_logs')
    }
    console.log('[LOG] Logs cleared')
  }

  // Get log statistics
  getStats() {
    const stats = {
      total: this.logs.length,
      byLevel: {} as Record<LogLevel, number>,
      byComponent: {} as Record<string, number>,
      sessionId: this.sessionId,
      userId: this.userId
    }

    this.logs.forEach(log => {
      stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1
      if (log.component) {
        stats.byComponent[log.component] = (stats.byComponent[log.component] || 0) + 1
      }
    })

    return stats
  }
}

// Global logger instance
let logger: Logger | null = null

export function getLogger(): Logger {
  if (!logger) {
    logger = new Logger({
      level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      enableConsole: true,
      enableStorage: true,
      maxStorageEntries: 1000,
      enableRemoteLogging: false, // Enable in production with proper endpoint
    })
  }
  return logger
}

// Convenience functions
export const log = {
  debug: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    getLogger().debug(message, context, component, action),
  
  info: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    getLogger().info(message, context, component, action),
  
  warn: (message: string, context?: Record<string, any>, component?: string, action?: string) =>
    getLogger().warn(message, context, component, action),
  
  error: (message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) =>
    getLogger().error(message, error, context, component, action),
  
  fatal: (message: string, error?: Error, context?: Record<string, any>, component?: string, action?: string) =>
    getLogger().fatal(message, error, context, component, action),
  
  userAction: (action: string, context?: Record<string, any>, component?: string) =>
    getLogger().userAction(action, context, component),
  
  apiCall: (endpoint: string, method: string, duration?: number, status?: number) =>
    getLogger().apiCall(endpoint, method, duration, status),
  
  performance: (metric: string, value: number, unit?: string) =>
    getLogger().performance(metric, value, unit),
  
  security: (event: string, context?: Record<string, any>) =>
    getLogger().security(event, context),
}

console.log('[LOG] Logging system initialized')
